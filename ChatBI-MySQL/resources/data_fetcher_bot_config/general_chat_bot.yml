agent_name: general_chat_bot
model: google/gemini-2.5-pro
model_settings:
  temperature: 0.1
  top_p: 0.9
tools:
  - name: search_feishu_docs
  - name: get_feishu_doc_content_tool
  - name: search_product_by_name
need_system_prompt: false
agent_description: general_chat_bot.md

agent_as_tool_description: |
  鲜沐ChatBI是一个专门为鲜沐公司设计的智能机器人，具备强大的产品知识问答和公司知识库检索能力。该助手专注于提供准确的产品信息查询、特性介绍、使用方法等专业解答，服务范围涵盖鲜果、乳制品、烘焙辅料、水吧辅料、西餐辅料、酒品、咖啡、糖等各类商品。

  ### 主要功能工具
  **1. search_feishu_docs**：核心知识库检索工具，用于从公司飞书文档中获取准确的产品知识、公司政策、流程规定等信息。该工具是获取专业知识的主要渠道。

  **2. get_feishu_doc_content_tool**：获取具体飞书文档内容的工具，用于深度获取文档详细信息。

  **3. search_product_by_name**：商品信息查询工具，用于根据商品名称搜索具体产品信息，包括产品分类、品牌属性等关键数据。

  ### 专业知识领域
  助手具备深度的商品分类知识，能够准确区分全品类商品（sub_type=1或2）、PB品（私有品牌：C味、Protag蛋白标签、SUMMERFARM等）和NB品（公共品牌）。在处理用户咨询时，能够基于这些专业知识提供精准解答。

  ### 服务特点与限制
  **服务优势**：提供专业的产品技术咨询、配方建议、操作指导，支持多轮对话的上下文理解，确保信息准确可靠。输出内容严格遵循markdown格式规范，以表格、列表等形式清晰呈现信息。

  **服务边界**：专注于知识问答、信息检索、专业咨询和商品信息查询，不提供价格查询、库存查询、订单查询等交易相关服务。

  ### AI调用建议
  在使用该助手时，AI应优先调用search_feishu_docs工具获取知识库信息，避免自行编造知识。当用户明确要求推荐商品时，应使用search_product_by_name工具获取准确商品信息。所有回复应保持专业性，以markdown格式输出，并在引用文档时提供明确的资料来源。