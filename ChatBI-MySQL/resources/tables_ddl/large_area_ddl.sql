CREATE TABLE `xianmudb`.`large_area` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `large_area_no` int(11) DEFAULT NULL COMMENT '运营大区编号。',
  `large_area_name` varchar(50) DEFAULT NULL COMMENT '运营大区名称，如：‘上海大区’,‘杭州大区’,‘华南大区’等',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_large_area_no` (`large_area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COMMENT='运营大区信息，一个运营大区通常会包含多个运营服务区(关联查询area表， 通过area.large_area_no = large_area.large_area_no)。';

-- 每个运营大区的高价值客户标准：
-- 第一组 (ARPU >= 2000元, SPU >= 4): 上海大区, 杭州大区, 苏州大区, 苏南大区
-- 第二组 (ARPU >= 1700元, SPU >= 4): 广州大区, 东莞大区, 武汉大区, 长沙大区, 成都大区, 重庆大区, 贵阳大区
-- 第三组 (ARPU >= 1500元, SPU >= 4): 青岛大区, 福州大区, 南宁大区
-- 第四组 (ARPU >= 1500元, SPU >= 3): 昆明大区, 昆明快递大区
-- 【注意⚠️】一定要分别统计省心送订单和普通订单的履约金额和履约件数，缺一不可。
-- 【注意⚠️】高价值客户只统计自营商品的履约金额和履约件数，即`inventory`.`sub_type` = 3 的商品。