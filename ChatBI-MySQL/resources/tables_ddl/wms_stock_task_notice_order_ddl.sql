-- --------------------------------------------------
-- 表名：wms_stock_task_notice_order
-- 中文名：出库通知单
-- 主要用途：记录各门店的销售出库通知单信息，支持按仓库、时间、状态等多维度统计销售出库情况。
-- 典型应用：可用于查询任意仓库在指定时间段内的销售出库明细，支撑销售出库数据分析。
-- --------------------------------------------------

CREATE TABLE `xianmudb`.`wms_stock_task_notice_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID，支持多租户隔离, 默认值为1,也就是只可查询鲜沐一个租户',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID，关联门店表：merchant.m_id',
  `shop_name` varchar(256) DEFAULT '' COMMENT '门店名称, merchant.mname的快照数据',
  `goods_supply_no` varchar(64) DEFAULT '' COMMENT '货品供应单号，外部业务单据编号',
  `out_order_no` varchar(64) NOT NULL DEFAULT '' COMMENT '外部订单号，业务系统订单编号, orders.order_no的快照数据',
  `warehouse_no` int(11) NOT NULL COMMENT '库存仓编号，出库仓库唯一标识, warehouse_storage_center.warehouse_no的快照数据',
  `store_no` int(11) DEFAULT NULL COMMENT '城配仓编号，配送中心编号, warehouse_logistics_center.store_no的快照数据',
  `out_order_type` int(11) NOT NULL COMMENT '订单类型：0-销售，1-补发，2-销售自提，3-样品，4-样品自提',
  `except_time` datetime DEFAULT NULL COMMENT '预计送达时间，客户期望收货时间，比如2025-06-18 00:00:00',
  `status` int(11) NOT NULL COMMENT '通知单状态：1-待处理，2-已处理，=2时表示通知单已处理完毕，代表已经配送出库完成，会送到客户手上。',
  `stock_task_id` bigint(20) DEFAULT NULL COMMENT '出库任务编码，关联出库任务表，stock_task.id的快照数据',
  `stock_task_create_time` datetime DEFAULT NULL COMMENT '出库任务生成时间',
  `receiver` varchar(255) DEFAULT '' COMMENT '订单收货人姓名',
  `phone` varchar(64) DEFAULT '' COMMENT '订单收货人联系方式',
  `province` varchar(64) DEFAULT '' COMMENT '订单收货地址省份，比如：浙江',
  `city` varchar(64) DEFAULT '' COMMENT '订单收货地址市，比如：杭州市',
  `area` varchar(64) DEFAULT '' COMMENT '订单收货地址区县，比如：西湖区',
  `detail_address` varchar(512) DEFAULT '' COMMENT '订单收货详细地址，比如：文一西路969号',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否软删除：0-未删除，1-已删除。只需查询=0的数据即可',
  `supply_mode` int(11) DEFAULT NULL COMMENT '货品供应单方式：0-代销入仓，1-代销不入仓',
  `notice_sku_flag` int(11) DEFAULT NULL COMMENT '通知单品标记：1-非代销不入仓品，2-代销不入仓品，3-混合品，4-POP品(顺鹿达商品)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_goods_supply_no` (`goods_supply_no`),
  KEY `idx_tenant` (`tenant_id`),
  KEY `idx_out_order_no` (`out_order_no`),
  KEY `idx_stock_task_id` (`stock_task_id`),
  KEY `idx_warehouse` (`warehouse_no`,`status`,`except_time`,`supply_mode`,`stock_task_id`),
  KEY `idx_except_time` (`except_time`)
) ENGINE=InnoDB AUTO_INCREMENT=4607558 DEFAULT CHARSET=utf8mb4 COMMENT='出库通知单表，记录门店销售出库通知单的主信息，支持多维度出库数据统计与分析';

-- 索引说明：
-- 要重点使用这个索引，因为它最高效区分度最高：`idx_warehouse` (`warehouse_no`,`status`,`except_time`,`supply_mode`,`stock_task_id`)
-- 一定要先去关联warehouse_storage_center 来获取仓库名称，以使用以上索引。
/*
-- ======================================================================
-- 查询目的：统计嘉兴总仓近7天安佳淡奶油产品的出库数量明细
-- 查询结果：返回日期、SKU编码、产品名称、规格重量、出库数量
-- ======================================================================

SELECT
    tmp.except_time,           -- 预计送达日期（按天聚合）
    tmp.sku,                   -- SKU编码
    spu.pd_name,               -- 产品名称（来自产品主表）
    sku.weight,                -- 商品规格重量（来自库存表）
    tmp.out_quantity           -- 出库数量（聚合求和）
FROM
    (
        -- ======================================================================
        -- 子查询：获取嘉兴总仓近7天的出库通知单明细数据
        -- 功能：按日期和SKU聚合出库数量
        -- ======================================================================
        SELECT
            DATE(T1.except_time) AS 'except_time',    -- 将预计送达时间按日期聚合
            T2.sku,                                    -- SKU编码
            T2.goods_name AS 'goods_name',            -- 商品名称（用于后续筛选）
            SUM(T2.quantity) AS 'out_quantity'        -- 按日期和SKU汇总出库数量
        FROM
            -- 出库通知单主表
            wms_stock_task_notice_order AS T1
            
            -- 关联出库通知单明细表
            INNER JOIN wms_stock_task_notice_order_detail AS T2 
                ON T1.id = T2.notice_order_id
            
            -- 关联仓库存储中心表，用于筛选指定仓库
            INNER JOIN warehouse_storage_center wsc 
                ON wsc.warehouse_no = T1.warehouse_no
                AND wsc.warehouse_name LIKE '嘉兴总仓%'    -- 筛选嘉兴总仓
                AND wsc.warehouse_name NOT LIKE '%测试%'    -- 排除测试仓库
        WHERE
            -- 时间范围：近7天（从当前日期往前推7天）
            T1.except_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            -- 通知单状态：2表示已处理
            AND T1.status = 2
        GROUP BY
            DATE(T1.except_time),    -- 按日期分组
            T2.sku                   -- 按SKU分组
    ) tmp
    
    -- 关联库存表，获取商品规格信息
    INNER JOIN inventory sku 
        ON sku.sku = tmp.sku
    
    -- 关联产品主表，获取产品名称
    INNER JOIN products spu 
        ON spu.pd_id = sku.pd_id
        
WHERE
    -- 商品名称筛选：只查询安佳淡奶油相关产品
    tmp.goods_name LIKE '%安佳淡奶油%'

-- ======================================================================
-- 查询逻辑说明：
-- 1. 子查询先从出库通知单获取基础数据，通过仓库表筛选嘉兴总仓
-- 2. 按日期和SKU聚合出库数量
-- 3. 外层查询关联库存表和产品表获取完整商品信息
-- 4. 最终筛选出安佳淡奶油相关产品
*/