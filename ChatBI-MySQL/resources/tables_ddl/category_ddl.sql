CREATE TABLE `xianmudb`.`category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '后端类目ID，主键自增长。其他表中的category_id都指向此表的id',
  `parent_id` int(11) DEFAULT NULL COMMENT '父后端类目ID，关联自身，用于构建后端类目层级关系。一级类目的parent_id为null，二级类目的parent_id指向一个一级类目的id，三级类目的parent_id则指向一个二级类目的id，以此类推。在查找指定类目时，请按需多次关联category表，以获取完整的类目路径',
  `category` varchar(255) DEFAULT NULL COMMENT '后端类目名称，如"香瓜"、"水果马蹄"等',
  `outdated` tinyint(1) NOT NULL DEFAULT '0' COMMENT '标记位-过时的后端类目：0表示正常后端类目，1表示已过时/删除的后端类目',
  `icon` varchar(255) DEFAULT NULL COMMENT '后端类目图标URL，用于前端展示',
  `type` int(2) DEFAULT NULL COMMENT '后端类目类型：1-全部，2-乳制品，3-非乳制品，4-水果',
  PRIMARY KEY (`id`),
  KEY `idx_outdated` (`outdated`,`type`),
  KEY `idx_parent_id` (`parent_id`,`outdated`)
) ENGINE=InnoDB AUTO_INCREMENT=1360 DEFAULT CHARSET=utf8 COMMENT='商品后端类目表，用于存储商品分类信息，该表为多级分类结构，通过parent_id建立层级关系。type字段为大类，包含水果、乳制品等大类；outdated标记失效后端类目。如"不知火丑橘"属于水果类(type=4)，其父后端类目ID为1140';