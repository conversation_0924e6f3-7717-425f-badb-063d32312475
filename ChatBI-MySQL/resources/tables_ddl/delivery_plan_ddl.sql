-- 【非常重要】普通订单的配送日通常是订单日往后推至少1天；省心送订单的话，用户可自行设定配送计划日，一般需要在下单后90天内配送完毕。
-- 【非常重要】省心送订单和普通订单的履约件数统计逻辑不同，分开处理更清晰，推荐使用UNION ALL方式，分别处理省心送订单和普通订单

-- 用户提到配送的点位数是指一个contact_id 就是一个点位数（其实就是配送的目的地）

CREATE TABLE `xianmudb`.`delivery_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(45) DEFAULT NULL COMMENT '订单号, 关联查询 `orders`.`order_no`',
  `status` smallint(6) DEFAULT '2' COMMENT '配送计划的状态: 2:已预约待占用库存(仅省心送订单有此状态，表示客户已经选定了配送日，但仓库还没有占用库存，仓库占用库存后会自动更新为‘3待配送’状态); 3:待配送(待履约); 6:已收货(代表履约完成); 11:已撤销',
  `delivery_time` date DEFAULT NULL COMMENT '计划配送日，如：2025-04-04。如查询某一日的已配送/履约完成的订单，请使用这个字段且status=6来进行过滤',
  `quantity` int(11) DEFAULT NULL COMMENT '本次的配送商品件数，如：2。请注意，只有省心送订单才可以从delivery_plan.quantity中获取履约件数，其他订单类型必须要从order_item.amount中获取',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '配送计划更新时间',
  `deliverytype` tinyint(4) DEFAULT '0' COMMENT '配送方式：0:城配仓配送（车队），1:自提(客户到仓库自提)',
  `time_frame` varchar(50) DEFAULT NULL COMMENT '计划的预计送达时间区间（比如10:00～11:00）',
  `admin_id` int(11) DEFAULT NULL COMMENT '下单门店所属的大客户ID，关联查询`admin`.`admin_id`且`admin_type`=0',
  `order_store_no` int(11) DEFAULT NULL COMMENT '下单时，库存扣减所用的库存仓编号，关联查询`warehouse_storage_center`.`warehouse_no`，库存仓名字则为:`warehouse_storage_center`.`warehouse_name`，可直接查询warehouse_storage_center获取库存仓名字和编号',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '配送计划生成时间',
  `contact_id` bigint(20) NOT NULL COMMENT '配送的目的地(收货地址)，通常一个contact_id就代表一个配送点位。关联查询`contact`.`contact_id`',
  PRIMARY KEY (`id`),
  KEY `ind_order_no` (`order_no`),
  KEY `ind_master_order_no` (`master_order_no`),
  KEY `ind_delivery_time` (`delivery_time`,`status`,`order_store_no`),
  KEY `ind_time_contact` (`contact_id`,`delivery_time`),
  KEY `ind_order_store_no_status` (`order_store_no`,`status`),
  KEY `idx_put_off_time` (`put_off_time`),
  KEY `idx_status_deliverytime` (`status`,`delivery_time`)
) ENGINE=InnoDB AUTO_INCREMENT=15402484 DEFAULT CHARSET=utf8 COMMENT='订单的配送计划表。通常而言，一笔订单只有一个配送计划，仅当订单类型为省心送时，才会有多个配送计划，此时，一个配送计划中仅包含部分商品，比如下单了10件，客户分5次配送，那么每个配送计划可能是2件商品。';

-- 例如，统计m_id = 282838的门店的履约件数和履约金额（其他门店的统计逻辑类似）
-- 先统计省心送订单：履约件数来自 delivery_plan.quantity
SELECT 
    orders.m_id,
    order_item.pd_name,
    SUM(delivery_plan.quantity) as 履约件数,  -- 省心送订单使用delivery_plan.quantity
    SUM(order_item.price * delivery_plan.quantity) as 履约金额  -- 省心送订单使用delivery_plan.quantity计算金额
FROM orders
INNER JOIN order_item ON orders.order_no = order_item.order_no 
INNER JOIN delivery_plan ON orders.order_no = delivery_plan.order_no
WHERE orders.m_id = 282838 
    AND delivery_plan.status = 6  -- 仅统计履约完成的订单
    AND orders.type = 1  -- 省心送订单
GROUP BY orders.m_id, order_item.pd_name
UNION ALL
-- 再普通订单：履约件数来自 order_item.amount  
SELECT 
    orders.m_id,
    order_item.pd_name,
    SUM(order_item.amount) as 履约件数,  -- 普通订单使用order_item.amount
    SUM(order_item.price * order_item.amount) as 履约金额  -- 普通订单使用order_item.amount计算金额
FROM orders
INNER JOIN order_item ON orders.order_no = order_item.order_no 
INNER JOIN delivery_plan ON orders.order_no = delivery_plan.order_no
WHERE orders.m_id = 282838 
    AND delivery_plan.status = 6  -- 仅统计履约完成的订单
    AND orders.type != 1  -- 非省心送订单（普通订单）
GROUP BY orders.m_id, order_item.pd_name;

-- 如果需要最终汇总结果，可以用以下查询（将上面的查询作为子查询）：
/*
SELECT 
    m_id,
    pd_name,
    SUM(履约件数) as 履约件数,
    SUM(履约金额) as 履约金额
FROM (
    -- 上面的UNION ALL查询
    ... 
) as merged_data
GROUP BY m_id, pd_name;
*/