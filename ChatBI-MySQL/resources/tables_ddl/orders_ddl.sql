-- 订单表。严禁使用DATE(o.order_time) = CURDATE()进行查询，这会导致索引失效，请使用o.order_time >= CURDATE() AND o.order_time < CURDATE() + INTERVAL 1 DAY进行查询。
-- 涉及到区域分析时，除非用户明确提到了运营服务区，否则应该总是使用门店的注册城市/省份来查询销量数据。

CREATE TABLE `xianmudb`.`orders` (
  `order_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '订单ID，主键自增',
  `order_no` varchar(36) DEFAULT NULL COMMENT '订单编号，唯一标识订单的字符串',
  `m_id` bigint(30) DEFAULT NULL COMMENT '商户编号，关联商户表:merchant.m_id',
  `order_time` datetime DEFAULT NULL COMMENT '订单生成时间，记录下单时间',
  `type` int(11) DEFAULT '0' COMMENT '订单类型：0-普通（一次购买仅可一次性配送完），1-省心送（一次购买可分多次配送），2-运费，3-代下单，10-虚拟商品（特指奶油黄金卡），30-‘顺鹿达’订单(鲜果POP)',
  `status` smallint(6) DEFAULT '1' COMMENT '订单状态：1-待支付，2-待配送，3-待收货，6-已收货，7-申请退款订单，8-已退款订单，9-支付失败订单，10-支付中断超时关闭订单，11-已撤销订单，14-手动关闭订单，15-人工退款中; 若用户无特别说明查询异常状态订单, 则默认查询状态in (2, 3, 6)的订单, 这些为正常状态',
  `delivery_fee` decimal(12,2) DEFAULT '0.00' COMMENT '配送费用，记录订单的配送费用金额',
  `total_price` decimal(12,2) DEFAULT '0.00' COMMENT '总金额，订单商品总价。请注意，如果关联`order_item`表查询时，必须要使用`order_item`.`actual_total_price`来计算订单总价，否则会因为一笔订单有多个order_item而重复计算订单总额',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注，订单的附加说明信息',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认收货时间，记录用户确认收货的时间',
  `out_times` int(2) DEFAULT '0' COMMENT '客户使用超时加单的次数。因我司每天有截单时间，过了截单时间下的订单，需要下下个配送周期才能配送。用户可用超时加单权益来实现过了截单时间但仍然享受及时的配送',
  `out_times_fee` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '客户使用超时加单权益时，所支付的费用。',
  `area_no` int(11) DEFAULT NULL COMMENT '商户下单时所属的运营服务区编号，关联运营服务区表:`area`.`area_no`',
  `m_size` varchar(20) DEFAULT NULL COMMENT '商户规模，取值范围[大客户、单店]',
  `account_id` bigint(30) DEFAULT NULL COMMENT '子账号ID，关联子账号表: `merchant_sub_account`.`account_id`，门店的子账户，门店可拥有多个子账户',
  `origin_price` decimal(10,2) DEFAULT NULL COMMENT '应付价格，订单原始价格',
  `receivable_status` smallint(6) NOT NULL DEFAULT '0' COMMENT '应收款状态：0-无应收款，1-未付款，2-部分付款，3-已付清',
  `admin_id` int(11) DEFAULT NULL COMMENT '门店所属的大客户ID，如果为NULL，则表示这是‘单店’类型的门店创建的订单。关联查询:`admin`.`admin_id` 且 admin_type = 0. 请注意这个字段不可用来判定私海关系，这个字段仅仅是表示这笔订单的门店是否从属于某个大客户。',
  `invoice_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发票状态：0-未开票，1-部分开票，2-已开票',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间，记录订单最后更新时间',
  `order_pay_type` tinyint(4) DEFAULT NULL COMMENT '代下单类型：1-账期，2-现结，3-账期代下，4-现结代下单',
  `selling_entity_name` varchar(30) NOT NULL DEFAULT '杭州鲜沐科技有限公司' COMMENT '销售主体名称，记录订单的销售主体',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `IN_orderno` (`order_no`,`status`),
  KEY `orders_ts_index` (`type`,`status`,`area_no`),
  KEY `rds_idx_2` (`status`,`m_id`,`order_time`,`area_no`),
  KEY `orders_area_no_index` (`area_no`,`status`),
  KEY `orders_time_index` (`order_time`,`status`),
  KEY `orders_sale_index` (`order_sale_type`,`status`,`area_no`),
  KEY `idx_financial_invoice_id` (`financial_invoice_id`),
  KEY `idx_mid` (`m_id`,`status`,`order_time`),
  KEY `idx_orderno_type_mid_areano` (`order_no`,`type`,`m_id`,`area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=14838710 DEFAULT CHARSET=utf8 COMMENT='订单表，记录所有订单的基本信息，包括订单状态、金额、门店ID、门店所属的运营服务区、订单配送信息等';

-- 注意，当orders.type=10时，表示这笔订单是虚拟商品（特指奶油黄金卡），无须关联order_item表。奶油黄金卡的有效期是1年，自下单日起算。
