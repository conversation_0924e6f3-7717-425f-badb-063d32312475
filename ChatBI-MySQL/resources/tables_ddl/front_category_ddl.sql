-- ----------------------------
-- 1. 前端类目(或者叫做商城类目)表
-- 作用：定义在商城前端展示给用户的分类，例如首页的“水果蔬菜”、“肉禽蛋品”等。也会叫做商城类目，销售BD提到品类、类目等等时，通常都是指前端类目。
-- 结构：通过 parent_id 形成层级关系（如“水果蔬菜”下有“精品水果”）。
-- 关联：一个前端类目可以关联到“后台类目”（relate_type=0）或直接关联到“商品”（relate_type=1）。
-- ----------------------------
CREATE TABLE `xianmudb`.`front_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '前端类目ID，主键，自增',
  `parent_id` int(11) DEFAULT NULL COMMENT '父级类目ID，关联本表的id字段，用于构建类目层级。顶级类目的parent_id为NULL或0',
  `name` varchar(20) DEFAULT NULL COMMENT '类目名称，例如“水果蔬菜”',
  `outdated` int(11) DEFAULT '0' COMMENT '是否已失效：0-有效，1-已失效。查询时通常需要过滤outdated=0的记录',
  `icon` varchar(50) DEFAULT NULL COMMENT '类目图标的URL链接',
  `f_category_type` tinyint(4) DEFAULT '1' COMMENT '类目所属商城类型：1-XM商城, 2-POP商城',
  `relate_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '关联类型：0-关联后台类目, 1-关联具体商品。决定了该类目下的内容是来自`front_category_to_category`表还是`front_category_to_sku`表',
  `updater` varchar(20) DEFAULT NULL COMMENT '最后更新人',
  `creator` varchar(20) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`,`outdated`),
  KEY `idx_status_parent` (`outdated`,`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=607 DEFAULT CHARSET=utf8 COMMENT='前端类目表，定义前端展示的分类层级和信息';

-- ----------------------------
-- 2. 前端类目与后台类目映射表
-- 作用：建立“前端类目”和“后端商品类目(category)”之间的多对多映射关系。
-- 场景：当一个前端类目（如“时令水果”）需要包含多个后台类目（如“柑橘橙柚”、“苹果”、“梨”）时使用。
-- ----------------------------
CREATE TABLE `xianmudb`.`front_category_to_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '映射关系ID，主键，自增',
  `front_category_id` int(11) DEFAULT NULL COMMENT '前端类目ID，关联`front_category`.`id`',
  `category_id` int(11) DEFAULT NULL COMMENT '后端商品类目ID，关联`category`.`id`',
  `creator` varchar(20) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_front_category` (`front_category_id`),
  KEY `idx_category` (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2065 DEFAULT CHARSET=utf8 COMMENT='前端类目与后台类目的映射关系表';

-- ----------------------------
-- 3. 前端类目与商品(SKU)关联表
-- 作用：将一个前端类目直接与具体的商品SKU进行关联。
-- 场景：用于创建“小编推荐”、“热销爆款”等由运营人员手动挑选商品组成的集合，这些商品可能来自不同的后台类目。
-- ----------------------------
CREATE TABLE `xianmudb`.`front_category_to_sku` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关联ID，主键，自增',
  `front_category_id` int(11) NOT NULL COMMENT '前端类目ID，关联`front_category`.`id`。通常是二级前端类目',
  `pd_name` varchar(240) DEFAULT NULL COMMENT '商品名称（冗余字段，方便快速查看）',
  `sku` varchar(50) NOT NULL COMMENT '商品SKU编码',
  `updater` varchar(20) DEFAULT NULL COMMENT '最后更新人',
  `creator` varchar(20) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `front_category_to_sku_index` (`front_category_id`,`sku`)
) ENGINE=InnoDB AUTO_INCREMENT=616 DEFAULT CHARSET=utf8 COMMENT='前端类目与具体商品的关联表';