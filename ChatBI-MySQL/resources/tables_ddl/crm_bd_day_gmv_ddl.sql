CREATE TABLE `xianmu_offline_db`.`crm_bd_day_gmv` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'BD日GMV记录ID，自增主键',
  `admin_id` int(11) NOT NULL COMMENT 'BD销售人员ID，关联xianmudb.crm_bd_org.bd_id，用于标识具体的销售人员，如：1001、1002',
  `admin_name` varchar(100) NOT NULL COMMENT 'BD销售人员姓名，关联xianmudb.crm_bd_org.bd_name，用于报表展示和查询，如：张三、李四',
  `total_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '当日总GMV（成交金额），包含所有渠道和类型的销售额，如：15680.50、32450.00',
  `single_store_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '单店GMV，单体门店客户的成交金额，如：8500.00、12300.50',
  `vip_customer_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '大客户GMV，VIP客户或品牌客户的成交金额，如：25000.00、18600.75',
  `fruit_category_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '鲜果类目GMV，水果商品的成交金额，如：5600.00、8900.25',
  `dairy_category_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '乳制品类目GMV，奶制品商品的成交金额，如：3200.00、4500.50',
  `non_dairy_category_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '非乳制品类目GMV，除奶制品外其他商品的成交金额，如：7800.00、9200.75',
  `self_brand_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '自营品牌GMV，公司自有品牌商品的成交金额，如：4500.00、6800.25',
  `reward_sku_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '固定奖励SKU的GMV，有特殊奖励政策的商品成交金额，如：1200.00、2300.50',
  `reward_sku_amount` int(11) NOT NULL DEFAULT '0' COMMENT '固定奖励SKU销量，有特殊奖励政策的商品销售数量，如：50、120',
  `core_merchant_amount` int(11) NOT NULL DEFAULT '0' COMMENT '核心客户数量，当日服务的核心商户数量，如：15、28',
  `monthly_active_amount` int(11) NOT NULL DEFAULT '0' COMMENT '月活客户数量，当月有下单行为的客户数量，如：180、250',
  `new_customer_amount` int(11) NOT NULL DEFAULT '0' COMMENT '拉新客户数量，当日新增的客户数量，如：8、15',
  `bd_performance` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT 'BD绩效金额，根据业绩计算的绩效奖金，如：2500.00、3800.50',
  `business_date` int(11) NOT NULL COMMENT '业务日期标记，格式yyyyMMdd，用于按日期查询统计，如：20250315、20250316',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，数据最后修改的时间戳',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，数据首次插入的时间戳',
  `ordinary_visit_count` int(11) NOT NULL DEFAULT '0' COMMENT '普通拜访次数，日常客户拜访的次数，如：12、18',
  `door_visit_count` int(11) NOT NULL DEFAULT '0' COMMENT '上门拜访次数，实地拜访客户的次数，如：5、8',
  `effective_visit_count` int(11) NOT NULL DEFAULT '0' COMMENT '有效拜访次数，产生实际业务价值的拜访次数，如：8、12',
  `valuable_visit_count` int(11) NOT NULL DEFAULT '0' COMMENT '价值拜访次数，高价值客户的拜访次数，如：3、6',
  `core_merchant_growth_level` decimal(5,1) NOT NULL DEFAULT '0.0' COMMENT '核心商户净增长牌级，核心客户增长情况的等级评分，如：3.5、4.2',
  `category_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '品类提成金额，按商品类别计算的提成收入，如：800.00、1200.50',
  `delivery_gmv` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '配送GMV，通过配送服务产生的成交金额，如：18500.00、25600.75',
  `spu_average_count` decimal(8,1) NOT NULL DEFAULT '0.0' COMMENT 'SPU平均数量，标准化产品单元的平均销售数量，如：15.5、22.8',
  `category_commission_multiply_delivery` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '品类提成乘以配送金额，用于特殊提成计算，如：2500.00、3800.75',
  `single_store_monthly_active` int(11) NOT NULL DEFAULT '0' COMMENT '单店月活数量，单体门店的月活跃客户数，如：45、68',
  `vip_monthly_active` int(11) NOT NULL DEFAULT '0' COMMENT 'VIP客户月活数量，大客户的月活跃数量，如：12、18',
  `single_store_spu_average` decimal(8,1) NOT NULL DEFAULT '0.0' COMMENT '单店SPU平均数量，单体门店的SPU平均销量，如：8.5、12.3',
  `vip_spu_average` decimal(8,1) NOT NULL DEFAULT '0.0' COMMENT 'VIP客户SPU平均数量，大客户的SPU平均销量，如：25.8、35.2',
  `shipped_order_count` int(11) NOT NULL DEFAULT '0' COMMENT '已配送订单数量，当月已完成配送的订单数，如：150、220',
  `ordinary_new_customer_amount` int(11) NOT NULL DEFAULT '0' COMMENT '普通拉新客户数量，通过常规渠道获得的新客户数，如：6、12',
  `register_no_order_count` int(11) NOT NULL DEFAULT '0' COMMENT '注册未下单客户数，已注册但未产生订单的客户数，如：25、40',
  `agent_goods_gmv` decimal(12,2) DEFAULT '0.00' COMMENT '代售商品GMV，代理销售商品的成交金额，如：3500.00、5200.25',
  `saas_total_gmv` decimal(12,2) DEFAULT NULL COMMENT 'SaaS总GMV，SaaS平台的总成交金额，如：45000.00、68500.50',
  `saas_warehouse_gmv` decimal(12,2) DEFAULT NULL COMMENT 'SaaS代仓GMV，SaaS平台代仓服务的成交金额，如：15000.00、22500.75',
  `saas_self_operated_gmv` decimal(12,2) DEFAULT NULL COMMENT 'SaaS自营GMV，SaaS平台自营商品的成交金额，如：28000.00、35600.25',
  `saas_markup_gmv` decimal(12,2) DEFAULT NULL COMMENT 'SaaS鲜沐商品加价GMV，SaaS平台鲜沐商品的加价收入，如：2500.00、3800.50',
  `saas_brand_count` int(11) DEFAULT NULL COMMENT 'SaaS品牌数量，SaaS平台服务的品牌数量，如：15、25',
  `saas_registered_store_count` int(11) DEFAULT NULL COMMENT 'SaaS注册门店数量，在SaaS平台注册的门店数量，如：120、180',
  `saas_order_store_count` int(11) DEFAULT NULL COMMENT 'SaaS下单门店数量，在SaaS平台有下单行为的门店数量，如：85、125',
  `saas_total_store_count` int(11) DEFAULT NULL COMMENT 'SaaS品牌门店总数，SaaS平台品牌的门店总数量，如：200、350',
  `saas_xianmu_gmv` decimal(12,2) DEFAULT NULL COMMENT 'SaaS鲜沐GMV，SaaS平台鲜沐品牌的成交金额，如：18000.00、26500.75',
  `order_merchant_count` int(11) NOT NULL DEFAULT '0' COMMENT '下单客户数量，当日有下单行为的客户总数，如：65、95',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_admin_id_business_date` (`admin_id`,`business_date`),
  KEY `idx_business_date` (`business_date`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_admin_name` (`admin_name`),
  KEY `idx_total_gmv` (`total_gmv`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_admin_date_gmv` (`admin_id`,`business_date`,`total_gmv`)
) ENGINE=InnoDB AUTO_INCREMENT=3327619 DEFAULT CHARSET=utf8 COMMENT='BD销售人员日业绩GMV统计表，记录每个BD销售人员每日的各项业绩指标，包括GMV、客户数量、拜访情况、提成等详细数据，用于销售业绩分析、提成计算和绩效考核。支持按日期、销售人员、GMV等多维度查询统计'