CREATE TABLE `xianmu_offline_db`.`bd_mtd_comm` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_bd_name` varchar(255) DEFAULT NULL COMMENT '最新归属BD',
  `last_bd_id` bigint DEFAULT NULL COMMENT 'BD_ID',
  `dep_level3` varchar(255) DEFAULT NULL COMMENT '大区',
  `dep_name` varchar(255) DEFAULT NULL COMMENT '区域',
  `total_score_num` double DEFAULT NULL COMMENT '利润积分',
  `bd_performance_rate` double DEFAULT NULL COMMENT '利润积分系数',
  `total_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '佣金总额',
  `a_commisstion_amt` decimal(10,2) DEFAULT NULL COMMENT '高价值客户总佣金',
  `a_cust_cnt` int DEFAULT NULL COMMENT '高价值客户数',
  `a_cust_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '高价值客户数佣金',
  `more_than_spu_cnt` bigint DEFAULT NULL COMMENT '高价值客户超额SPU数',
  `a_spu_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '高价值超额spu佣金',
  `category_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '品类推广总佣金',
  `old_cust_comm` decimal(10,2) DEFAULT NULL COMMENT '存量客户品类佣金',
  `new_cust_comm` decimal(10,2) DEFAULT NULL COMMENT '新增客户品类佣金',
  `big_sku_cnt` double DEFAULT NULL COMMENT '品类推广件数_大规格',
  `old_big_sku_cnt` double DEFAULT NULL COMMENT '存量客户推广件数_大规格',
  `new_big_sku_cnt` double DEFAULT NULL COMMENT '新增客户推广件数_大规格',
  `dlv_real_amt` decimal(10,2) DEFAULT NULL COMMENT 'MTD履约实付GMV',
  `item_profit_amt` decimal(10,2) DEFAULT NULL COMMENT 'MTD履约商品毛利润',
  `ds` varchar(255) DEFAULT NULL COMMENT '数据日期',
  `dlv_spu_cnt` bigint DEFAULT NULL COMMENT 'MTD履约SPU数',
  `more_than_spu_cust_cnt` bigint DEFAULT NULL COMMENT '高价值超额spu客户数',
  PRIMARY KEY (`id`),
  KEY `idx_bd_mtd_comm_last_bd_id_a_cust_cnt` (`last_bd_id`,`a_cust_cnt`),
  KEY `idx_bd_mtd_comm_last_bd_id_more_than_spu_cnt` (`last_bd_id`,`more_than_spu_cnt`),
  KEY `idx_bd_mtd_comm_last_bd_id_a_spu_comm_amt` (`last_bd_id`,`a_spu_comm_amt`),
  KEY `idx_bd_mtd_comm_last_bd_id_dlv_real_amt` (`last_bd_id`,`dlv_real_amt`)
) ENGINE=InnoDB AUTO_INCREMENT=11024 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='MTD单销售绩效汇总表'