CREATE TABLE `xianmudb`.`warehouse_batch_prove_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  `type` tinyint(4) DEFAULT NULL COMMENT '任务类型编号 9预约入库  10调拨入库 11采购入库',
  `source_id` varchar(30) DEFAULT NULL COMMENT '来源唯一id',
  `sku` varchar(30) NOT NULL COMMENT 'sku编号, 关联商品SKU表:inventory.sku',
  `batch` varchar(30) NOT NULL COMMENT '批次编号, 关联采购订单号purchase_no，可以通过采购预约单找到warehouse_no与仓库关联',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `quality_date` date DEFAULT NULL COMMENT '保质日期',
  `quality_inspection_report` varchar(255) DEFAULT NULL COMMENT '质检报告, 排空值逻辑为(quality_inspection_report IS NOT NULL and quality_inspection_report != '')，非空情况可能存在多条(以逗号分隔)，值不是以http前缀开头的话就是相对链接，需要补充链接前缀https://azure.summerfarm.net/，多条的情况也要补充',
  `customs_declaration_certificate` varchar(255) DEFAULT NULL COMMENT '报关证明, 排空值逻辑为(customs_declaration_certificate IS NOT NULL and customs_declaration_certificate != '')，非空情况可能存在多条(以逗号分隔)，值不是以http前缀开头的话就是相对链接，需要补充链接前缀https://azure.summerfarm.net/，多条的情况也要补充',
  `nucleic_acid_detection` varchar(255) DEFAULT NULL COMMENT '核酸检测, 排空值逻辑为(nucleic_acid_detection IS NOT NULL and nucleic_acid_detection != '')，非空情况可能存在多条(以逗号分隔)，值不是以http前缀开头的话就是相对链接，需要补充链接前缀https://azure.summerfarm.net/，多条的情况也要补充',
  `disinfection_certificate` varchar(255) DEFAULT NULL COMMENT '消毒证明, 排空值逻辑为(disinfection_certificate IS NOT NULL and disinfection_certificate != '')，非空情况可能存在多条(以逗号分隔)，值不是以http前缀开头的话就是相对链接，需要补充链接前缀https://azure.summerfarm.net/，多条的情况也要补充',
  `supervision_warehouse_certificate` varchar(255) DEFAULT NULL COMMENT '监管仓证明, 排空值逻辑为(supervision_warehouse_certificate IS NOT NULL and supervision_warehouse_certificate != '')，非空情况可能存在多条(以逗号分隔)，值不是以http前缀开头的话就是相对链接，需要补充链接前缀https://azure.summerfarm.net/，多条的情况也要补充',
  `detection_result` tinyint(4) DEFAULT NULL COMMENT '检测结果 0 合格 1 不合格',
  `sampling_base` decimal(10,2) DEFAULT NULL COMMENT '抽样基数',
  `number_samples` decimal(10,2) DEFAULT NULL COMMENT '抽样数',
  `inhibition_rate` decimal(10,2) DEFAULT NULL COMMENT '抑制率',
  `pesticide_residue_pictures` varchar(500) DEFAULT NULL COMMENT '农药残留报告图片, 排空值逻辑为(pesticide_residue_pictures IS NOT NULL and pesticide_residue_pictures != '')，非空情况可能存在多条(以逗号分隔)，值不是以http前缀开头的话就是相对链接，需要补充链接前缀https://azure.summerfarm.net/，多条的情况也要补充',
  `creator` int(11) DEFAULT NULL COMMENT '创建人admin_id',
  `proof_complete` tinyint(4) DEFAULT NULL COMMENT '证件是否齐全',
  `tenant_id` bigint(20) unsigned DEFAULT '1' COMMENT '租户id(saas品牌方)',
  `creator_name` varchar(255) DEFAULT NULL COMMENT '操作人名称',
  PRIMARY KEY (`id`),
  KEY `idx_batch_production_date` (`batch`,`production_date`),
  KEY `idx_sourceid_type` (`source_id`,`type`),
  KEY `idx_sku_batch_production_date` (`sku`,`batch`,`production_date`),
  KEY `idx_type_sourceid` (`type`,`source_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1344468 DEFAULT CHARSET=utf8 COMMENT='库存商品证件信息变更记录表，记录商品的证件信息，包括质检报告、报关证明、核酸检测、消毒证明、监管仓证明、农药残留报告图片'
;