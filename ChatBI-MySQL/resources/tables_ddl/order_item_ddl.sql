CREATE TABLE `xianmudb`.`order_item` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '订单项ID，自增主键',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称（快照），来源自`products`.`pd_name`, 在统计商品的销量时，可直接使用该字段，而不需要关联`products`表。如：‘8寸10切约翰丹尼慕斯蛋糕’、‘晴王青提(阳光玫瑰)’、‘TS韩国幼砂糖’',
  `sku_name` varchar(255) DEFAULT NULL COMMENT 'SKU名称（快照），关联`inventory`.`sku_name`, 如：‘约翰丹尼大孔乳酪慕斯蛋糕’，请注意⚠️这个字段非常少用到，建议使用`pd_name`',
  `sku` varchar(30) NOT NULL COMMENT '产品编号，唯一标识商品，关联`inventory`.`sku`如：5455443070、16823457512',
  `weight` varchar(100) DEFAULT NULL COMMENT '重量/规格，描述商品规格信息，如：毛重24-26斤/一级/特大果9个',
  `order_no` varchar(36) DEFAULT NULL COMMENT '订单编号，关联订单表:orders.order_no，如：0125IX2SAE0328191411',
  `category_id` int(11) DEFAULT NULL COMMENT '商品后端类目ID, 关联 `category`.`category_id`，可以用于过滤商品后端类目',
  `amount` int(10) DEFAULT NULL COMMENT '购买数量，如：2、3',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '购买时的商品单价（实际单价，使用了优惠之后的单价），如：102.25',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '购买时的商品单价（原价），如：104.00',
  `add_time` datetime DEFAULT NULL COMMENT '购买时间，如：2025-03-28 19:14:36',
  `status` int(11) DEFAULT '1' COMMENT '订单项状态：1待支付，2待配送，3待收货，6已收货，7申请退款订单，8已退款订单，9支付失败订单，10支付中断超时关闭订单，11已撤销订单，14手动关闭订单，15人工退款中',
  `volume` varchar(255) DEFAULT NULL COMMENT '体积，如：0.450*0.450*0.170',
  `weight_num` decimal(10,2) DEFAULT '0.00' COMMENT '重量(kg)，如：13.00、2.90',
  `use_coupon` tinyint(4) DEFAULT NULL COMMENT '是否用了优惠券，0：否，1：是',
  `product_type` int(11) DEFAULT '0' COMMENT '商品类型：0普通商品，1赠品',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `actual_total_price` decimal(10,2) unsigned DEFAULT NULL COMMENT '商品实付总价（数量*单价），如：208.00、216.00',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `order_item_sku_index` (`sku`),
  KEY `idx_orderno_sku_weight_pdname` (`order_no`,`sku`,`weight`,`pd_name`)
) ENGINE=InnoDB AUTO_INCREMENT=33655113 DEFAULT CHARSET=utf8 COMMENT='订单商品明细表，记录订单中每个商品的具体信息，包括商品名称、规格、单价、数量、总价、状态等，用于订单管理和商品追溯。每个订单可能有多个订单项';