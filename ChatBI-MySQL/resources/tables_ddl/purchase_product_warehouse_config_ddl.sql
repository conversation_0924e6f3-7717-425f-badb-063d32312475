CREATE TABLE `xianmudb`.`purchase_product_warehouse_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `pd_id` bigint(20) NOT NULL COMMENT '商品SPU编号，关联products.pd_id',
  `warehouse_no` int(11) NOT NULL COMMENT '仓库编号，关联warehouse_storage_center.warehouse_no',
  `purchase_type` int(11) NOT NULL COMMENT '采购类型：1-直采, 2-非直采',
  `admin_id` int(11) DEFAULT NULL COMMENT '采购负责人ID，关联admin.admin_id',
  `admin_name` varchar(32) DEFAULT NULL COMMENT '采购负责人姓名',
  `creator` int(11) DEFAULT NULL COMMENT '创建人ID，关联admin.admin_id',
  `safe_water_level` int(11) DEFAULT NULL COMMENT '安全库存水位，低于此数量需要补货',
  `backlog_day` int(11) DEFAULT NULL COMMENT '备货天数，提前多少天开始备货',
  `pd_no` varchar(50) DEFAULT NULL COMMENT '商品编号，商品的业务编号',
  PRIMARY KEY (`id`),
  KEY `idx_pd_warehouse` (`pd_id`,`warehouse_no`),
  KEY `idx_warehouse_no` (`warehouse_no`),
  KEY `idx_admin` (`admin_id`),
  KEY `idx_pd_no_warehouse` (`pd_no`,`warehouse_no`)
) ENGINE=InnoDB AUTO_INCREMENT=336861 DEFAULT CHARSET=utf8 COMMENT='商品仓库采购配置表，记录每个商品在各个仓库的采购负责人及库存管理配置信息';
