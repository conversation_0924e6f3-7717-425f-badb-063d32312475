CREATE TABLE `xianmudb`.`warehouse_storage_center` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `warehouse_no` int(11) DEFAULT NULL COMMENT '仓库编号，比如：10',
  `warehouse_name` varchar(50) DEFAULT NULL COMMENT '库存仓名称，比如：嘉兴总仓、东莞冷冻总仓、南京总仓等。查询通常需要过滤包含"测试"名称的测试仓',
  `type` int(11) DEFAULT NULL COMMENT '仓库类型：0、本部仓 1、外部仓 2、合伙人仓',
  `status` int(11) DEFAULT '1' COMMENT '仓库开放状态。0、不开放 1、开放，只需要查询开放的仓库（status=1）即可',
  `address` varchar(255) DEFAULT NULL COMMENT '仓库所在的具体地址',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户id(saas品牌方)，这里，除非用户有特殊需求，否则默认查询租户id为1的数据',
  PRIMARY KEY (`id`),
  UNIQUE KEY `warehouse_storage_center_warehouse_no_uindex` (`warehouse_no`),
  KEY `idx_warehouse_no_status` (`warehouse_no`,`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=330 DEFAULT CHARSET=utf8 COMMENT='库存仓基本信息，包含了库存仓名字、编码、以及地理位置等';