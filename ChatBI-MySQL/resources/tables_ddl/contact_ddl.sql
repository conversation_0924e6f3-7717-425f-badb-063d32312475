-- 【重要】联系人表是商户的收货地址信息表，一个商户(m_id)可以有多个联系人地址
-- 【重要】contact_id是配送的目的地标识，通常一个contact_id就代表一个配送点位
-- 【重要】is_default=1表示该地址是商户的默认收货地址
-- 【重要】status字段含义：1-正常/审核通过，2-删除，3-待审核，4-审核不通过。查询有效地址时应使用status=1
-- 【重要】distance字段记录该地址与仓库的距离(单位：米)，用于配送路线规划
-- 【重要】poi_note字段存储高德地图的经纬度坐标，格式为"经度,纬度"

CREATE TABLE `xianmudb`.`contact` (
  `contact_id` bigint(30) NOT NULL AUTO_INCREMENT,
  `m_id` bigint(30) DEFAULT NULL COMMENT '商户id，关联查询`merchant`.`m_id`',
  `contact` varchar(200) DEFAULT NULL COMMENT '联系人姓名',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `weixincode` varchar(30) DEFAULT NULL COMMENT '微信号',
  `province` varchar(20) NOT NULL COMMENT '省份，如：浙江，注意不带"省"字',
  `city` varchar(20) NOT NULL COMMENT '城市，如：杭州市',
  `area` varchar(50) DEFAULT '' COMMENT '区县，如：西湖区',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `status` int(11) DEFAULT '3' COMMENT '地址状态：1-正常/审核通过，2-删除，3-待审核，4-审核不通过',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `is_default` int(3) DEFAULT '0' COMMENT '是否默认地址：0-非默认，1-默认地址',
  `house_number` varchar(255) DEFAULT NULL COMMENT '门牌号/详细位置描述',
  `store_no` int(11) DEFAULT NULL COMMENT '配送仓编号（通常也叫城配仓编号，区别于库存仓编号，这个是配送站点的意思，不做库存管理），关联查询`warehouse_logistics_center`.`store_no`',
  PRIMARY KEY (`contact_id`),
  KEY `IN_phone` (`phone`) USING HASH,
  KEY `idx_status` (`status`),
  KEY `idx_city_area` (`city`,`area`),
  KEY `idx_merchant_status_is_default` (`m_id`,`status`,`is_default`)
) ENGINE=InnoDB AUTO_INCREMENT=730385 DEFAULT CHARSET=utf8 COMMENT='联系人收货地址表，存储商户的收货地址信息，每个contact_id代表一个配送点位';

-- 查询商户的有效收货地址示例
SELECT 
    c.contact_id,
    c.m_id,
    m.mname as 商户名称,
    c.contact as 联系人,
    c.phone as 联系电话,
    CONCAT(c.province, c.city, c.area, c.address) as 完整地址,
    c.is_default as 是否默认地址,
    c.distance as 距离仓库距离_米,
    w.warehouse_name as 配送仓库名称
FROM contact c
LEFT JOIN merchant m ON c.m_id = m.m_id
LEFT JOIN warehouse_storage_center w ON c.store_no = w.warehouse_no
WHERE c.status = 1  -- 仅查询有效地址
    AND c.m_id = 334343  -- 指定商户ID
ORDER BY c.is_default DESC, c.create_time DESC;

-- 统计各城市的配送点位数量
SELECT 
    c.province,
    c.city,
    COUNT(DISTINCT c.contact_id) as 配送点位数,
    COUNT(DISTINCT c.m_id) as 商户数量
FROM contact c
WHERE c.status = 1  -- 仅统计有效地址
GROUP BY c.province, c.city
ORDER BY 配送点位数 DESC;
