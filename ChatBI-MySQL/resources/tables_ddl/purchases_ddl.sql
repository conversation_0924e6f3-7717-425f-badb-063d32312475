CREATE TABLE `xianmudb`.`purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `purchase_no` varchar(30) DEFAULT NULL COMMENT '采购单号，唯一标识采购单',
  `purchaser` varchar(15) DEFAULT NULL COMMENT '采购负责人姓名',
  `receiver` varchar(15) DEFAULT NULL COMMENT '收货负责人姓名',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `state` int(11) DEFAULT '0' COMMENT '采购单状态：-1-作废, 0-计划制定, 1-已发布, 2-待供应商确认, 3-审核中, 4-审核拒绝',
  `area_no` int(11) DEFAULT NULL COMMENT '仓库编号，关联库存中心表:warehouse_storage_center.warehouse_no',
  `delivery_type` int(2) DEFAULT '1' COMMENT '配送类型：1-送货到库, 2-自提',
  `remark` varchar(50) DEFAULT NULL COMMENT '备注',
  `purchases_type` tinyint(4) DEFAULT NULL COMMENT '采购类型：0-正常采购, 1-直发采购',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int(11) DEFAULT NULL COMMENT '采购单状态(发票)：0-未归档, 1-已归档',
  `is_arrange` tinyint(4) DEFAULT '0' COMMENT '可预约入库标识：0-不可预约, 1-可预约',
  `process_state` tinyint(4) DEFAULT '0' COMMENT '入库进度：0-待入库, 1-部分入库, 2-已入库',
  `arrange_time` date DEFAULT NULL COMMENT '计划制定状态下预约入库时间',
  `arrange_remark` varchar(100) DEFAULT NULL COMMENT '计划制定状态下预约备注',
  `operator_type` tinyint(4) DEFAULT NULL COMMENT '供应商是否已经确认该笔采购单：0-未确认, 1-已确认, 2-已拒绝',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID，关联人员表:admin.admin_id',
  `source` varchar(255) NOT NULL DEFAULT 'xianmu' COMMENT '来源：xianmu（鲜沐），或者：saas（SaaS租户）',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID，默认1，代表鲜沐，默认情况下只取这个值。saas租户ID从2开始',
  `delivery_time` datetime DEFAULT NULL COMMENT '（计划制定下）物流用车时间',
  `tms_dist_site_id` bigint(20) DEFAULT NULL COMMENT '（计划制定下）物流用车发货地址',
  `business_type` tinyint(4) DEFAULT NULL COMMENT '业务类型：1-代销不入库类型, 2-代销不入仓-备货, 3-代销不入仓-预提, 4-POP, 5-库存初始化',
  `take_time` datetime DEFAULT NULL COMMENT '提货时间',
  `pso_no` varchar(255) DEFAULT NULL COMMENT 'OFC采购供应单号',
  `origin_system` varchar(255) DEFAULT NULL COMMENT '来源系统(SCP_SYSTEM:scp计划)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `no_index` (`purchase_no`),
  KEY `area_no_index` (`area_no`),
  KEY `time_index` (`purchase_time`),
  KEY `state_idx` (`tenant_id`,`state`),
  KEY `idx_pso` (`pso_no`(64))
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购单主表，记录采购单的基本信息，一个采购单可以发起多张入库预约单stock_arrange，预约后确定送货日期, "采购在途"指采购单的未完成数量和采购预约单未完成数量';
