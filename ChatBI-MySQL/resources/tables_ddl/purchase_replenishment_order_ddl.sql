CREATE TABLE `xianmudb`.`purchase_replenishment_order` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `pd_id` bigint(20) NOT NULL COMMENT '商品SPU ID，关联商品SPU表:products.pd_id',
  `warehouse_no` int(11) NOT NULL COMMENT '仓库编号，关联仓库表:warehouse_storage_center.warehouse_no',
  `sku_id` varchar(30) NOT NULL COMMENT 'SKU编码，关联商品SKU表:inventory.sku',
  `view_date` datetime NOT NULL COMMENT '日期，任务创建日期',
  `order_status` int(11) NOT NULL COMMENT '补货单状态：1-待确认, 2-已发起, 3-已关闭, 4-自动关闭',
  `supplier_id` int(11) NOT NULL COMMENT '供应商ID，关联供应商表:supplier.id',
  `admin_id` int(11) NOT NULL COMMENT '采购负责人ID，关联人员表:admin.admin_id',
  `final_replenishment_quantity` int(11) DEFAULT NULL COMMENT '最终补货量',
  `final_supplier_id` int(11) NOT NULL COMMENT '最终供应商ID，关联供应商表:supplier.id',
  `final_supplier_name` varchar(64) NOT NULL COMMENT '最终供应商名称，supplier表中name字段快照',
  `final_admin_id` int(11) NOT NULL COMMENT '最终采购负责人ID，关联人员表:admin.admin_id',
  `final_admin_name` varchar(32) NOT NULL COMMENT '最终采购负责人姓名',
  `relation_type` int(11) DEFAULT NULL COMMENT '关联操作类型：1-采购单',
  `relation_id` varchar(30) DEFAULT NULL COMMENT '关联操作ID，当relation_type=1时，关联采购单表:purchases.purchase_no',
  `create_date` int(11) NOT NULL COMMENT '创建日期',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` int(11) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `creator` int(11) DEFAULT NULL COMMENT '创建人',
  `del_flag` int(11) NOT NULL COMMENT '删除标记：0-未删除, 1-已删除',
  `source` tinyint(4) DEFAULT '0' COMMENT '来源：0-缺货提醒, 1-补货计划',
  `replenishment_plan_purchase_task_id` bigint(20) DEFAULT NULL COMMENT '补货任务ID，补货计划中的任务ID',
  PRIMARY KEY (`id`),
  KEY `idx_date` (`create_date`,`view_date`),
  KEY `idx_warehouse_no` (`warehouse_no`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_pd_name` (`pd_name`)
) ENGINE=InnoDB AUTO_INCREMENT=451 DEFAULT CHARSET=utf8 COMMENT='采购任务表-采购单前置确定的计划单据';
