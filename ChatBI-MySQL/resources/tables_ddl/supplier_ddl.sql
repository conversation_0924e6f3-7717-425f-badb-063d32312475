CREATE TABLE `xianmudb`.`supplier` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `manager` varchar(50) DEFAULT NULL COMMENT '供应商管理人姓名',
  `invoice` tinyint(1) DEFAULT '1' COMMENT '是否开发票：0-否, 1-是',
  `supplier_type` tinyint(4) DEFAULT '0' COMMENT '供应商类型：0-企业（生产商）, 1-个人, 2-企业（经销商）',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用, 1-停用, 2-审核中, 3-已关闭',
  `audit_pass_date` date DEFAULT NULL COMMENT '审核通过日期（用于计算剩余天数）',
  `creator` varchar(50) DEFAULT '' COMMENT '创建人姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(45) DEFAULT NULL COMMENT '最后修改人姓名',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `qr_code_show_supplier_switch` tinyint(4) NOT NULL DEFAULT '0' COMMENT '二维码是否展示供应商信息：0-不展示, 1-展示',
  `source` varchar(255) NOT NULL DEFAULT 'xianmu' COMMENT '采购单来源，默认xianmu。其他来源：saas',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID，默认1(鲜沐)，其他ID时表示是其他SaaS租户。默认情况下，应该都只需要取tenant_id=1的数据',
  `business_type` tinyint(4) DEFAULT NULL COMMENT '业务类型：1-代销',
  `customer_supplier_id` varchar(64) DEFAULT NULL COMMENT '外部供应商ID',
  PRIMARY KEY (`id`),
  KEY `index_name` (`name`),
  KEY `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商表，记录供应商的基本信息';
