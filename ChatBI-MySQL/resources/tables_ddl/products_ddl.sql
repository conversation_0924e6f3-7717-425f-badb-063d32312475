CREATE TABLE `xianmudb`.`products` (
  `pd_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '商品ID，主键，自增长',
  `category_id` int(11) DEFAULT NULL COMMENT '商品后端类目ID，关联category.id表',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称。一般来说商品名字中会带有品牌名字，如"安佳淡奶油"',
  `outdated` int(11) NOT NULL DEFAULT '0' COMMENT '商品状态：-1:上新中, 0:有效, 1:已删除',
  `storage_location` tinyint(4) DEFAULT '0' COMMENT '商品的最适保存区域：0-未分类 1-冷冻 2-冷藏 3-常温',
  `storage_method` varchar(255) DEFAULT NULL COMMENT '存储方式说明',
  `picture_path` varchar(1255) DEFAULT NULL COMMENT '商品主图URL',
  `quality_time` int(10) NOT NULL DEFAULT '0' COMMENT '保质期时长，如24表示24个月',
  `quality_time_unit` varchar(30) NOT NULL DEFAULT 'day' COMMENT '保质期单位，如"month"表示月',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `create_type` int(11) DEFAULT '0' COMMENT '上新类型：0-平台 1-大客户 2-其他',
  `audit_status` int(11) DEFAULT '1' COMMENT '审核状态：0-待上新 1-上新成功 2-上新失败',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`pd_id`),
  KEY `products_to_category_fk` (`category_id`),
  KEY `products_pdname_index` (`pd_name`)
) ENGINE=InnoDB AUTO_INCREMENT=15534 DEFAULT CHARSET=utf8 COMMENT='商品基础信息表。每个products可能有多个inventory(SKU， 通过pd_id关联)';

-- 请注意，获取商品的品牌是可用product_property_value表来获取，比如想要获取‘鲜沐农场’品牌的所有商品，则可使用：
SELECT
  ppv.pd_id,
  ppv.`products_property_value` as `商品品牌名称`,
  pd.pd_name as `商品名称`
FROM
  `products_property_value` ppv
  INNER JOIN products pd on pd.`pd_id` = ppv.`pd_id`
WHERE
  ppv.`products_property_id` = 2
  and ppv.products_property_value = '鲜沐农场';