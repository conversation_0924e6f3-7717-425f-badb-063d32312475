{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}/src"}}, {"name": "Python: Flask App", "type": "python", "request": "launch", "program": "${workspaceFolder}/app.py", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}/src", "FLASK_ENV": "development", "FLASK_DEBUG": "1"}}]}