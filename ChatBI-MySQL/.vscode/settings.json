{"python.terminal.activateEnvironment": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.extraPaths": ["./src"], "python.analysis.include": ["./src/**"], "python.venvPath": ".", "python.venvFolders": ["venv", ".venv", "env", ".env"], "files.exclude": {"**/__pycache__": true, "**/*.pyc": true}, "python.testing.pytestEnabled": true}