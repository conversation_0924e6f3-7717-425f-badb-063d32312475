"""
飞书消息内容获取和解析模块
"""

import json
import base64
from dataclasses import dataclass
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET

# 创建lark客户端
lark_client = (
    lark.Client.builder()
    .app_id(APP_ID)
    .app_secret(APP_SECRET)
    .timeout(3)
    .log_level(lark.LogLevel.INFO)
    .build()
)


@dataclass
class MessageContent:
    """消息内容"""

    chat_id: str | None  # 群聊ID
    text: str  # 消息文本
    image_url: str | None  # base64编码的图片内容，可以用于AI对话


def get_message_content(message_id: str) -> MessageContent | None:
    """获取消息内容，并处理不同格式的返回"""
    logger.info(f"获取消息内容: {message_id}")
    request: GetMessageRequest = (
        GetMessageRequest.builder().message_id(message_id).build()
    )
    response: GetMessageResponse = lark_client.im.v1.message.get(request)

    if not response.success():
        logger.error(
            f"获取消息内容失败: {response.code} {response.msg} {response.raw.content}"
        )
        return None

    if not response.data.items:
        logger.warning(f"消息 {message_id} 内容为空.")
        return MessageContent(chat_id=None, text="", image_url=None)

    item = response.data.items[0]
    chat_id = item.chat_id
    msg_type = item.msg_type
    body_content_str = item.body.content

    texts = []
    image_key = None
    image_url_b64 = None

    try:
        if body_content_str:
            logger.info(f"message_id:{message_id}, body_content_str:{body_content_str}")
            content_json = json.loads(body_content_str)

            if msg_type == "text":
                if "text" in content_json:
                    texts.append(content_json["text"])
            elif msg_type == "image":
                if "image_key" in content_json:
                    image_key = content_json["image_key"]
            elif msg_type == "post":
                # "post" 类型的 content 是一个包含 title 和 content 的JSON字符串
                # content_json["content"] 是一个二维数组，代表富文本内容
                if "content" in content_json and isinstance(
                    content_json["content"], list
                ):
                    for block in content_json["content"]:
                        if isinstance(block, list):
                            for element in block:
                                if isinstance(element, dict):
                                    tag = element.get("tag")
                                    if tag == "text":
                                        texts.append(element.get("text", ""))
                                    elif tag == "img":
                                        image_key = element.get(
                                            "image_key"
                                        )  # 保留最后一个图片
            else:
                # 对于其他未知或未显式处理的类型，如果 content 是简单文本，则直接视为文本
                # 或者如果它是一个JSON但不是我们期望的结构，我们可能无法从中提取文本/图片，都直接视为文本
                texts.append(body_content_str)

        # 如果直接从 body 获取到 image_key (例如一些简单图片消息可能直接有这个字段)
        if hasattr(item.body, "image_key") and item.body.image_key:
            image_key = item.body.image_key

    except json.JSONDecodeError:
        logger.warning(
            f"消息 {message_id} 的 body.content 不是有效的JSON字符串: {body_content_str}, 将其视为纯文本."
        )
        texts.append(body_content_str)
    except Exception as e:
        logger.exception(f"解析消息 {message_id} 内容时出错: {e}")
        if body_content_str and not texts:  # 避免重复添加
            texts.append(body_content_str)

    extracted_text = " ".join(filter(None, texts)).strip()

    if image_key:
        # 调用 get_message_image_content 获取图片的 base64 内容
        # 注意：get_message_image_content 内部会处理 API 调用和 base64 编码
        logger.info(f"为消息 {message_id} 获取图片内容, image_key: {image_key}")
        image_url_b64 = get_message_image_content(message_id, image_key)
        if image_url_b64 is None:
            logger.warning(
                f"未能为消息 {message_id} 的 image_key {image_key} 获取到图片内容"
            )

    logger.info(
        f"解析后消息内容 - 文本: '{extracted_text[:100]}...', 是否存在图片: {bool(image_url_b64)}"
    )
    return MessageContent(
        chat_id=chat_id,
        text=extracted_text,
        image_url=(
            f"data:image/jpeg;base64,{image_url_b64}" if bool(image_url_b64) else None
        ),
    )


def get_message_image_content(message_id: str, file_key: str) -> str | None:
    """获取消息图片内容"""
    logger.info(f"获取消息图片内容: {message_id}, {file_key}")
    request: GetMessageResourceRequest = (
        GetMessageResourceRequest.builder()
        .message_id(message_id)
        .file_key(file_key)
        .type("image")
        .build()
    )
    response: GetMessageResourceResponse = lark_client.im.v1.message_resource.get(
        request
    )
    if not response.success():
        logger.exception(f"获取消息图片内容失败: {response.raw.content}")
        return None

    # 读取图片文件并转换为base64编码
    return base64.b64encode(response.file.read()).decode("utf-8")
