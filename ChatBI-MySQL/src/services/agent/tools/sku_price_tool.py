import requests
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.logger import logger
from src.services.xianmudb.query_service import execute_business_query


def sku_price_tool(sku_list: List[str], m_id: Optional[int] = None, mname: Optional[str] = None, phone: Optional[str] = None) -> Dict[str, Any]:
    """
    根据商户ID、商户名称或商户手机号以及SKU列表，获取商品价格。这个接口会返回商品的实时价格，包括客户使用优惠券后的到手价。
    可用于回答“请问XX门店的日清山茶花高筋粉的价格是多少？”等问题。

    参数:
        sku_list: SKU code 列表, 例如 ['abc123456789', 'def987654321']
        m_id: 商户ID，例如 213456
        mname: 商户名称，例如 "XX水果店"
        phone: 商户手机号，例如 "***********"
        【注意】m_id, mname, phone 三个参数至少提供一个。

    返回:
        dict: SKU价格信息，包括商品的到手价。
    """
    if not m_id and not mname and not phone:
        return {"error": "请至少提供商户ID、商户名称或手机号中的一个。"}

    target_m_id = m_id

    if not target_m_id:
        # 如果没有直接提供 m_id，则通过 mname 或 phone 查询
        if mname:
            sql = f"SELECT m_id FROM merchant WHERE mname = '{mname}' LIMIT 1"
            logger.info(f"根据门店名称 {mname} 查询门店ID: {sql}")
        elif phone:
            sql = f"SELECT m_id FROM merchant WHERE phone = '{phone}' LIMIT 1"
            logger.info(f"根据门店手机号 {phone} 查询门店ID: {sql}")
        
        result = execute_business_query(sql)

        if not result.success or not result.data:
            error_msg = f"查询门店失败: {result.error or '未找到匹配的门店'}"
            logger.error(error_msg)
            return {"error": error_msg}
        
        target_m_id = result.data[0]['m_id']
        logger.info(f"查询到门店ID: {target_m_id}")

    url = "https://h5.summerfarm.net/price/batch/take-actual-price"
    
    # Prepare the SKU list for the request body
    sku_list_with_quantity = [{"sku": sku, "quantity": 1} for sku in sku_list]
    
    # Generate the signature
    today_str = datetime.now().strftime('%Y%m%d')
    signature_str = f"summerfarm-mall{today_str}"
    signature = hashlib.md5(signature_str.encode('utf-8')).hexdigest()
    
    payload = {
        "mId": target_m_id,
        "skuList": sku_list_with_quantity,
        "signature": signature,
        "minLadderPrice": 1
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()  # Raise an exception for bad status codes
        data = response.json()
        if data and "data" in data:
            logger.info(f"获取SKU价格成功: {data['data']}")
            return data["data"]
        return {"error": f"获取SKU价格失败:{data}"}
    except requests.exceptions.RequestException as e:
        logger.error(f"获取SKU价格失败: {e}")
        return {"error": str(e)}

tool_manager.register_as_function_tool(sku_price_tool)
