"""
查询运行器 - 向后兼容接口
现在使用统一的BaseQueryProcessor架构
"""
from typing import Generator

from src.services.agent.api_query_processor import run_agent_query as api_run_agent_query
from src.utils.logger import logger
from src.utils.system_monitor import start_system_monitoring, stop_system_monitoring, log_current_usage


def run_agent_query(
        user_query: str,
        user_info: dict = {},
        access_token: str = None,
        conversation_id: str = None,
        images: list = None,
        model_name: str = None,
) -> Generator:
    """
    查询处理函数 - 向后兼容接口
    现在直接使用APIQueryProcessor的实现
    
    Args:
        user_query: 用户查询
        user_info: 用户信息字典
        access_token: 访问令牌
        conversation_id: 对话ID
        images: 图片列表
        model_name: 模型名称
        
    Returns:
        Generator: 流式响应生成器
    """
    return api_run_agent_query(
        user_query, user_info, access_token, conversation_id, images, model_name
    )


# 优雅关闭函数
def shutdown_gracefully():
    """优雅关闭所有资源"""
    logger.info("开始优雅关闭runner.py...")
    
    # 记录关闭前的资源使用情况
    log_current_usage()
    
    # 停止系统监控
    stop_system_monitoring()
    
    # 记录关闭后的资源使用情况
    log_current_usage()
    
    logger.info("runner.py优雅关闭完成")


# 启动系统监控
start_system_monitoring()

# 在应用退出时调用
import atexit
atexit.register(shutdown_gracefully)
