"""
API endpoints for authentication.
"""
import os
import traceback
from flask import render_template, session, url_for, Blueprint, request, make_response, jsonify, redirect
from datetime import datetime

from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.services.auth.user_login_with_feishu import login, callback, login_required, HOST_NAME
from src.services.dashboard.dashboard_service import ADMIN_USERS, admin_required

# Create a Blueprint for auth endpoints
# Using url_prefix='' to maintain original URL paths
auth_bp = Blueprint('auth', __name__, url_prefix='')


@auth_bp.route('/')
@login_required
def index():
    """
    GET / endpoint that renders the main HTML page.
    """
    user_info = session.get("user_info")
    user_name = user_info.get("name")
    # 优先使用数据库中的avatar字段，如果没有则使用飞书API返回的avatar_thumb
    user_avatar_thumb = user_info.get("avatar") or user_info.get("avatar_thumb")
    user_email = get_valid_user_email(user_info)
    job_title = user_info.get("job_title")
    # Check if the user is an admin by checking against the ADMIN_USERS set defined in dashboard_apis
    is_admin = user_name in ADMIN_USERS

    # Get APPLICATION_ROOT from environment
    APPLICATION_ROOT = os.getenv("APPLICATION_ROOT", "/crm-chatbi")

    return render_template(
        "index.html",
        user_name=user_name,
        user_avatar_thumb=user_avatar_thumb,
        hostname=HOST_NAME,
        cache_control_timestamp=datetime.now().timestamp(),
        user_email=user_email,
        job_title=job_title,
        is_admin=is_admin,  # Pass admin status to template
        app_root=APPLICATION_ROOT,  # Add app_root to the template context
    )


from flask import request # 导入 request

@auth_bp.route('/login')
def login_route():
    import urllib.parse # 导入 urllib.parse 以处理 URL 组件

    """
    GET /login 接口，启动登录流程。
    读取 'next' 和 'chat' 查询参数，以确定登录后的最终目标路径。
    例如：/login?next=%2F&chat=some_chat_id
    """
    # 从查询参数中获取 'next' 值，如果不存在则默认为 '/'
    base_destination_path = request.args.get('next', '/')
    # 从查询参数中获取 'chat' 值
    chat_id = request.args.get('chat')

    final_destination_path = base_destination_path

    if chat_id:
        # 解析 base_destination_path (可能包含基础路径和现有查询参数)
        parsed_url = urllib.parse.urlparse(base_destination_path)
        # 解析现有查询参数为字典
        current_query_params = urllib.parse.parse_qs(parsed_url.query)
        
        # 添加或更新 'chat' 参数
        # parse_qs 的值是列表，所以我们也用列表形式赋值
        current_query_params['chat'] = [chat_id] 
        
        # 将更新后的查询参数字典重新编码为查询字符串
        new_query_string = urllib.parse.urlencode(current_query_params, doseq=True)
        
        # 使用新的查询字符串重新构建目标路径
        # urlunparse 需要一个包含6个组件的元组：(scheme, netloc, path, params, query, fragment)
        final_destination_path = urllib.parse.urlunparse(
            (parsed_url.scheme, parsed_url.netloc, parsed_url.path, 
             parsed_url.params, new_query_string, parsed_url.fragment)
        )

    logger.info(
        f"Login route called: next='{base_destination_path}', chat='{chat_id if chat_id else ''}'. "
        f"Computed final destination for Feishu state: '{final_destination_path}'"
    )
    
    # 将最终计算出的、包含 chat_id (如果存在) 的目标路径传递给 login 服务函数
    # login 服务函数会将其用作飞书认证回调后的重定向目标 (存储在 state 参数中)
    return login(destination_path=final_destination_path)


@auth_bp.route('/callback')
def callback_route():
    """
    GET /callback endpoint that handles the authentication callback.
    """
    try:
        return callback()
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.exception(f"Error in callback: {e}")
        return f"Error occurred during authentication callback: {str(e)}<br><pre>{error_trace}</pre>", 500


@auth_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """
    GET /dashboard endpoint that renders the admin dashboard page.
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    # 优先使用数据库中的avatar字段，如果没有则使用飞书API返回的avatar_thumb，最后使用默认头像
    user_avatar_thumb = user_info.get("avatar") or user_info.get("avatar_thumb") or url_for('static', filename='img/default-avatar.png')

    # Get APPLICATION_ROOT from environment
    APPLICATION_ROOT = os.getenv("APPLICATION_ROOT", "/crm-chatbi")

    logger.info(f"Rendering dashboard for admin user: {username}")
    return render_template(
        'dashboard.html',
        user_name=username,
        user_avatar_thumb=user_avatar_thumb,
        app_root=APPLICATION_ROOT,
        cache_control_timestamp=datetime.now().timestamp(),
    )


@auth_bp.route('/logout', methods=['POST'])
def logout():
    """
    用户登出接口
    """
    try:
        user_info = session.get("user_info")
        if user_info:
            logger.info(f"用户登出: {user_info.get('name')}")

        # 清除客户端cookies
        resp = make_response(jsonify({
            "success": True,
            "message": "登出成功"
        }))

        # 清除JWT token cookie
        resp.set_cookie("token", '', expires=0)

        # 清除Flask session
        session.clear()

        return resp

    except Exception as e:
        logger.error(f"用户登出失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "message": f"登出失败: {str(e)}"
        }), 500


@auth_bp.route('/session/status', methods=['GET'])
def session_status():
    """
    获取当前用户的登录状态
    """
    try:
        jwt_token = request.cookies.get("jwt_token")
        user_info = session.get("user_info")

        return jsonify({
            "success": True,
            "data": {
                "is_logged_in": bool(jwt_token and user_info),
                "has_token": bool(jwt_token),
                "user_info": user_info if user_info else None
            }
        })

    except Exception as e:
        logger.error(f"获取session状态失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "message": f"获取状态失败: {str(e)}"
        }), 500


@auth_bp.route('/session/ensure-login', methods=['GET'])
@login_required
def ensure_login():
    """
    确保用户登录的端点，用于飞书机器人引导用户授权
    如果用户已登录则返回成功信息，否则引导用户进行飞书登录
    """
    user_info = session.get("user_info")
    union_id = user_info.get("union_id") if user_info else None
    if not union_id:
        logger.warning("用户没有union_id，需要再次授权")
        return redirect("/login?next=/session/ensure-login")
    return "授权成功！您可以关闭此页面了。", 200
