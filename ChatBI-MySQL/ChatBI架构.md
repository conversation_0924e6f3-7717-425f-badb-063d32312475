# ChatBI-MySQL 项目架构说明文档

## 目录

1. [项目概述](#项目概述)
2. [核心技术栈](#核心技术栈)
3. [整体架构图](#整体架构图)
4. [Agent as Tool 架构详图](#agent-as-tool-架构详图)
5. [核心模块详解](#核心模块详解)
6. [请求处理流程](#请求处理流程)
7. [关键设计模式](#关键设计模式)
8. [技术实现细节](#技术实现细节)
9. [部署与配置](#部署与配置)
10. [开发指南](#开发指南)
11. [监控与运维](#监控与运维)
12. [故障排查](#故障排查)

## 项目概述

ChatBI-MySQL 是一个基于飞书机器人的智能商业分析系统，采用 **Agent as Tool 架构**，为鲜沐公司提供销售分析、仓储物流、知识查询等多领域的智能分析服务。

### 核心特性
- 🤖 **智能Agent协调**: CoordinatorBot统一协调多个专业分析工具
- 🔄 **流式响应**: 实时流式输出，提升用户体验
- 🔐 **长效认证**: 飞书OAuth2.0 + JWT + 自动Token刷新
- 📊 **双端支持**: 网页版 + 飞书机器人双端访问
- 🏗️ **DDD架构**: 领域驱动设计，代码结构清晰
- ⚡ **高性能**: 连接池 + 异步处理 + 事件驱动

## 核心技术栈

- **后端框架**: Flask + Python 3.8+
- **数据库**: MySQL (业务数据库 + ChatBI数据库)
- **AI框架**: Agents库 (支持多种LLM模型)
- **消息平台**: 飞书开放平台 (WebSocket + HTTP API)
- **认证系统**: 飞书OAuth2.0 + JWT + Session管理
- **架构模式**: DDD (领域驱动设计) + Agent as Tool

## 整体架构图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           ChatBI-MySQL 系统架构                              │
├─────────────────────────────────────────────────────────────────────────────┤
│  用户接入层                                                                    │
│  ┌─────────────────────┐              ┌─────────────────────┐                │
│  │     网页版前端        │              │     飞书机器人        │                │
│  │   (Web Portal)      │              │   (Feishu Bot)      │                │
│  │  ┌───────────────┐  │              │  ┌───────────────┐  │                │
│  │  │ React/Vue前端 │  │              │  │ 交互式卡片     │  │                │
│  │  │ SSE流式响应   │  │              │  │ WebSocket连接 │  │                │
│  │  └───────────────┘  │              │  └───────────────┘  │                │
│  └─────────────────────┘              └─────────────────────┘                │
│           │                                       │                          │
│           │ HTTPS/SSE                            │ WebSocket                 │
│           ▼                                       ▼                          │
├─────────────────────────────────────────────────────────────────────────────┤
│  API网关层 (Flask Application)                                                │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │  Auth API   │ │  Query API  │ │Dashboard API│ │  Feishu     │       │ │
│  │  │ 飞书OAuth   │ │ 流式查询    │ │ 管理面板    │ │ WebSocket   │       │ │
│  │  │ JWT验证     │ │ SSE响应     │ │ 统计监控    │ │ 事件处理    │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│           │                       │                       │                  │
├─────────────────────────────────────────────────────────────────────────────┤
│  业务服务层                                                                    │
│  ┌─────────────────────┐              ┌─────────────────────┐                │
│  │     认证服务          │              │     Agent服务        │                │
│  │ ┌─────────────────┐ │              │ ┌─────────────────┐ │                │
│  │ │ 飞书OAuth2.0    │ │              │ │ CoordinatorBot  │ │                │
│  │ │ JWT Token管理   │ │              │ │ (主协调者)      │ │                │
│  │ │ Session会话     │ │              │ │ ┌─────────────┐ │ │                │
│  │ │ Token自动刷新   │ │              │ │ │销售分析工具 │ │ │                │
│  │ └─────────────────┘ │              │ │ │仓储物流工具 │ │ │                │
│  │ ┌─────────────────┐ │              │ │ │通用知识工具 │ │ │                │
│  │ │ 消息队列服务    │ │              │ │ │商品搜索工具 │ │ │                │
│  │ │ 流式事件处理    │ │              │ │ └─────────────┘ │ │                │
│  │ │ 线程池管理      │ │              │ └─────────────────┘ │                │
│  │ └─────────────────┘ │              └─────────────────────┘                │
│  └─────────────────────┘                       │                            │
│           │                                     │                            │
├─────────────────────────────────────────────────────────────────────────────┤
│  数据访问层                                                                    │
│  ┌─────────────────────┐              ┌─────────────────────┐                │
│  │   ChatBI数据库       │              │    业务数据库        │                │
│  │   (MySQL)           │              │    (MySQL)          │                │
│  │ ┌─────────────────┐ │              │ ┌─────────────────┐ │                │
│  │ │ 用户会话表      │ │              │ │ orders订单表    │ │                │
│  │ │ 聊天历史表      │ │              │ │ merchant商户表  │ │                │
│  │ │ 好案例表        │ │              │ │ 商品库存表      │ │                │
│  │ │ 用户信息表      │ │              │ │ area运营区表    │ │                │
│  │ └─────────────────┘ │              │ └─────────────────┘ │                │
│  │ 连接池: 10个连接     │              │ 连接池: 20个连接     │                │
│  └─────────────────────┘              └─────────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Agent as Tool 架构详图

```
                    CoordinatorBot (协调者机器人)
                           │
                           ▼
    ┌─────────────────────────────────────────────────────────────┐
    │                   工具选择与调用                              │
    │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
    │  │ 查询分析器  │ │ 工具路由器  │ │ 结果聚合器  │           │
    │  └─────────────┘ └─────────────┘ └─────────────┘           │
    └─────────────────────────────────────────────────────────────┘
                           │
                           ▼
    ┌─────────────────────────────────────────────────────────────┐
    │                    专业Agent工具                             │
    │                                                             │
    │  ┌─────────────────┐  ┌─────────────────┐                  │
    │  │ sales_order_    │  │ warehouse_and_  │                  │
    │  │ analytics       │  │ fulfillment     │                  │
    │  │ (销售分析专家)   │  │ (仓储物流专家)   │                  │
    │  │ ┌─────────────┐ │  │ ┌─────────────┐ │                  │
    │  │ │SQL查询工具  │ │  │ │库存分析工具 │ │                  │
    │  │ │数据聚合工具 │ │  │ │配送路径工具 │ │                  │
    │  │ │图表生成工具 │ │  │ │仓储优化工具 │ │                  │
    │  │ └─────────────┘ │  │ └─────────────┘ │                  │
    │  └─────────────────┘  └─────────────────┘                  │
    │                                                             │
    │  ┌─────────────────┐  ┌─────────────────┐                  │
    │  │ general_chat_   │  │ search_product_ │                  │
    │  │ bot             │  │ by_name         │                  │
    │  │ (通用知识专家)   │  │ (商品搜索工具)   │                  │
    │  │ ┌─────────────┐ │  │ ┌─────────────┐ │                  │
    │  │ │知识库查询   │ │  │ │商品名称匹配 │ │                  │
    │  │ │FAQ回答     │ │  │ │SKU确认     │ │                  │
    │  │ │业务咨询     │ │  │ │规格筛选     │ │                  │
    │  │ └─────────────┘ │  │ └─────────────┘ │                  │
    │  └─────────────────┘  └─────────────────┘                  │
    └─────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. 认证与会话管理模块

#### 技术特色
- **长效认证机制**: 基于飞书OAuth2.0 + 自生成Session ID + JWT Token的三层认证
- **自动Token刷新**: 后台服务每5分钟检查即将过期的Token并自动刷新
- **单点登录**: 同一用户多设备登录时自动停用旧会话

#### 核心组件
- `user_login_with_feishu.py`: 飞书OAuth2.0认证流程
- `user_session_service.py`: 用户会话领域服务
- `token_refresh_service.py`: Token自动刷新服务
- `UserSession`: 会话领域模型

### 2. Agent智能分析模块

#### Agent as Tool 架构
采用协调者模式，将专业分析能力封装为可复用的工具：

```
CoordinatorBot (协调者)
├── 商品搜索工具
├── sales_order_analytics (销售分析专家)
├── warehouse_and_fulfillment (仓储物流专家)
└── general_chat_bot (通用知识专家)
```

#### 技术特色
- **配置驱动**: 通过YAML配置文件定义Agent能力和工具
- **动态工具调用**: 根据用户查询智能选择合适的专业工具
- **流式响应**: 支持实时流式输出，提升用户体验
- **错误恢复**: 统一的错误处理和重试机制

#### 核心组件
- `CoordinatorBot`: 主协调者，负责分析查询并调用专业工具
- `DataFetcherBot`: 数据获取专家，支持SQL查询和数据分析
- `ToolManager`: 工具管理器，负责工具注册和调用
- `StreamProcessor`: 流式处理器，处理Agent响应并更新飞书卡片

### 3. 飞书集成模块

#### 技术特色
- **WebSocket长连接**: 实时接收飞书消息事件
- **交互式卡片**: 支持复杂的卡片交互和实时更新
- **线程池处理**: 专用线程池处理消息，避免阻塞WebSocket连接
- **事件驱动**: 基于事件驱动的消息处理机制

#### 核心组件
- `client.py`: 飞书WebSocket客户端
- `event_handlers.py`: 事件处理器，处理消息和卡片操作
- `stream_processor.py`: 流式处理器，实时更新卡片内容
- `message_core.py`: 消息发送核心服务

### 4. 数据库连接池模块

#### 技术特色
- **双数据库架构**: ChatBI数据库(系统数据) + 业务数据库(分析数据)
- **连接池管理**: MySQL连接池，支持高并发访问
- **查询安全**: 自动禁止危险的写操作，仅允许只读查询
- **异步查询**: 专用线程池执行数据库查询，避免阻塞

#### 核心组件
- `connection.py`: 数据库连接池管理
- `query_service.py`: 业务查询服务
- `Database`: 数据库枚举，区分不同数据库

## 请求处理流程

### 网页版请求流程

```mermaid
sequenceDiagram
    participant User as 用户浏览器
    participant Auth as 认证服务
    participant API as Query API
    participant Agent as Agent服务
    participant DB as 数据库

    User->>Auth: 1. 访问首页
    Auth->>Auth: 2. 检查JWT Token
    alt Token有效
        Auth->>User: 3. 返回主页面
    else Token无效/过期
        Auth->>User: 3. 重定向到飞书登录
        User->>Auth: 4. 飞书OAuth回调
        Auth->>DB: 5. 保存用户会话
        Auth->>User: 6. 设置JWT Cookie
    end
    
    User->>API: 7. 发送查询请求
    API->>Agent: 8. 调用CoordinatorBot
    Agent->>Agent: 9. 分析查询并选择工具
    Agent->>DB: 10. 执行SQL查询
    DB->>Agent: 11. 返回查询结果
    Agent->>API: 12. 流式返回分析结果
    API->>User: 13. SSE流式响应
```

### 飞书机器人请求流程

```mermaid
sequenceDiagram
    participant User as 飞书用户
    participant Feishu as 飞书平台
    participant Bot as 机器人服务
    participant Agent as Agent服务
    participant DB as 数据库

    User->>Feishu: 1. 发送消息给机器人
    Feishu->>Bot: 2. WebSocket推送消息事件
    Bot->>Bot: 3. 线程池处理消息
    
    alt 用户未授权
        Bot->>User: 4. 发送授权提醒卡片
    else 用户已授权
        Bot->>Bot: 5. 创建初始卡片
        Bot->>Agent: 6. 调用CoordinatorBot
        Agent->>Agent: 7. 流式处理查询
        loop 流式更新
            Agent->>Bot: 8. 发送流式内容
            Bot->>Feishu: 9. 更新卡片内容
        end
        Agent->>DB: 10. 保存聊天历史
        Bot->>Feishu: 11. 发送最终卡片
    end
```

## 关键设计模式

### 1. 领域驱动设计 (DDD)
- **领域模型**: UserSession, QueryRequest, StreamEvent等
- **领域服务**: UserSessionService, AgentExecutionService等
- **仓储模式**: UserSessionRepository等

### 2. Agent as Tool 模式
- **协调者模式**: CoordinatorBot作为主协调者
- **工具封装**: 专业Agent封装为可复用工具
- **动态调用**: 根据查询内容智能选择工具

### 3. 事件驱动架构
- **消息队列**: 基于Queue的事件传递
- **流式处理**: StreamEvent驱动的实时响应
- **异步处理**: 线程池 + 异步协程

## 部署与配置

### 环境变量配置
```bash
# 飞书配置
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret

# 数据库配置
CHATBI_MYSQL_HOST=mysql-host
CHATBI_MYSQL_PORT=3308
BUSINESS_DB_HOST=business-mysql-host
BUSINESS_DB_PORT=3306

# 应用配置
APPLICATION_ROOT=/crm-chatbi
ENABLE_BOT_MESSAGE_PROCESSING=true
```

### 启动命令
```bash
python app.py --port 5700 --debug
```

## 技术实现细节

### 1. 消息队列与事件驱动

#### StreamEvent 事件模型
```python
@dataclass
class StreamEvent:
    event_type: str    # 事件类型: raw_response_event, tool_call_log, handoff_log
    content: str       # 事件内容
    data: Optional[Any] = None  # 附加数据
```

#### 事件处理流程
1. **Agent执行**: CoordinatorBot调用专业工具
2. **事件生成**: 工具执行过程产生StreamEvent
3. **队列传递**: 通过QueueMessageService传递事件
4. **流式响应**: StreamResponseGenerator生成客户端响应

### 2. 飞书卡片交互机制

#### 卡片生命周期
```
初始卡片 → 思考中更新 → 流式内容更新 → 最终结果卡片
    ↓           ↓            ↓            ↓
  创建卡片    更新thinking   更新content   添加反馈按钮
```

#### 卡片更新策略
- **批量更新**: 累积一定字符数后批量更新，减少API调用
- **超时保护**: 超过5分钟自动停止更新，避免长时间占用
- **错误恢复**: 更新失败时自动重试，确保用户体验

### 3. 数据库连接池优化

#### 连接池配置
```python
# ChatBI数据库池 (系统数据)
CHATBI_POOL_SIZE = 10
CHATBI_CONNECT_TIMEOUT = 10

# 业务数据库池 (分析数据)
BUSINESS_POOL_SIZE = 20
BUSINESS_CONNECT_TIMEOUT = 3
```

#### 查询安全机制
- **SQL注入防护**: 参数化查询，禁止字符串拼接
- **写操作禁止**: 自动检测并阻止DELETE/UPDATE/INSERT操作
- **超时控制**: 查询超时5分钟自动终止

## 开发指南

### 快速上手 (30分钟)

#### 1. 环境准备 (5分钟)
```bash
# 克隆项目
git clone <repository>
cd ChatBI-MySQL

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件，填入飞书和数据库配置
```

#### 2. 理解核心概念 (10分钟)
- **CoordinatorBot**: 主协调者，分析用户查询并调用专业工具
- **DataFetcherBot**: 数据获取专家，执行SQL查询和数据分析
- **StreamEvent**: 事件驱动的消息传递机制
- **UserSession**: 用户会话管理，支持长效认证

#### 3. 运行项目 (5分钟)
```bash
# 启动开发服务器
python app.py --port 5700 --debug

# 访问网页版
http://localhost:5700

# 配置飞书机器人回调地址
https://your-domain.com/feishu/webhook
```

#### 4. 第一个功能开发 (10分钟)
参考现有的销售分析工具，快速创建新的分析功能。

### 新增Agent工具

#### 1. 创建配置文件
在 `resources/data_fetcher_bot_config/` 创建 `new_agent.yml`:
```yaml
agent_name: new_agent
model: anthropic/claude-sonnet-4
model_settings:
  temperature: 0.1
  top_p: 0.9
tools:
  - name: fetch_mysql_sql_result
  - name: fetch_ddl_for_table
agent_tables:
  - name: "your_table"
    desc: "表描述"
agent_description: |
  你是专门处理XX业务的专家...
agent_as_tool_description: |
  专业的XX分析工具，能够...
```

#### 2. 注册到协调者
在 `coordinator_bot.yml` 中添加:
```yaml
agent_tools:
  - name: new_agent
```

#### 3. 定义专用工具 (可选)
```python
@tool_manager.register_as_function_tool
def your_custom_tool(param: str) -> str:
    """自定义工具函数"""
    # 实现工具逻辑
    return result
```

### 新增API端点

#### 1. 创建Blueprint
```python
# src/api/new_api.py
from flask import Blueprint, request, jsonify
from src.services.auth.user_login_with_feishu import login_required

new_bp = Blueprint('new_api', __name__, url_prefix='/api/new')

@new_bp.route('/endpoint', methods=['POST'])
@login_required
def new_endpoint():
    # 实现API逻辑
    return jsonify({"status": "success"})
```

#### 2. 注册Blueprint
```python
# src/api/__init__.py
from .new_api import new_bp

def register_routes(app: Flask):
    app.register_blueprint(new_bp)
```

### 数据库操作最佳实践

#### 1. 查询业务数据
```python
from src.services.xianmudb.query_service import execute_business_query

# 同步查询
result = execute_business_query("SELECT * FROM orders LIMIT 10")
if result.success:
    data = result.data
    columns = result.columns

# 异步查询 (推荐)
result = await execute_business_query_async("SELECT * FROM orders LIMIT 10")
```

#### 2. 操作系统数据
```python
from src.db.connection import execute_db_query, Database

# 查询ChatBI数据库
result = execute_db_query(
    "SELECT * FROM chat_history WHERE user_id = %s",
    params=(user_id,),
    fetch='all',
    database=Database.CHATBI
)
```

#### 3. 安全注意事项
- ✅ 使用参数化查询: `"SELECT * FROM table WHERE id = %s", (id,)`
- ❌ 避免字符串拼接: `f"SELECT * FROM table WHERE id = {id}"`
- ✅ 仅执行SELECT查询
- ❌ 禁止DELETE/UPDATE/INSERT操作

## 监控与运维

### 日志系统
- **结构化日志**: 使用统一的logger，支持JSON格式输出
- **关键节点**: Agent执行、数据库查询、飞书API调用
- **错误追踪**: 完整的异常堆栈信息

### 性能监控
- **数据库连接池**: 实时监控连接数和等待时间
- **Agent执行统计**: 记录各Agent的执行时间和成功率
- **内存使用**: 监控Python进程内存占用

### 安全机制
- **认证层**: 飞书OAuth2.0 + JWT双重验证
- **授权层**: 基于用户角色的权限控制
- **数据层**: SQL注入防护 + 查询权限限制

## 故障排查

### 常见问题

#### 1. 飞书机器人无响应
- 检查WebSocket连接状态
- 验证飞书App配置
- 查看事件处理器日志

#### 2. Agent执行超时
- 检查数据库连接池状态
- 优化SQL查询性能
- 调整超时配置

#### 3. 用户认证失败
- 验证JWT Token有效性
- 检查Session是否过期
- 确认飞书OAuth配置

### 调试技巧
1. **开启详细日志**: 设置 `--debug` 参数
2. **使用断点调试**: 在关键代码处添加断点
3. **监控数据库**: 查看慢查询日志
4. **检查网络**: 验证飞书API连通性

## 总结

ChatBI-MySQL项目采用现代化的微服务架构设计，具有以下核心优势：

### 架构优势
1. **Agent as Tool模式**: 将专业分析能力封装为可复用工具，提高代码复用性和可维护性
2. **事件驱动架构**: 基于StreamEvent的消息传递，支持流式响应和异步处理
3. **领域驱动设计**: 清晰的领域边界，便于团队协作和功能扩展
4. **双端统一**: 网页版和飞书机器人共享相同的业务逻辑

### 技术亮点
1. **智能协调**: CoordinatorBot能够智能分析用户查询并选择最合适的专业工具
2. **长效认证**: 创新的三层认证机制，用户体验流畅
3. **流式交互**: 实时流式响应，支持复杂分析任务的进度展示
4. **高可用性**: 连接池、重试机制、超时保护等确保系统稳定性

### 开发效率
- **配置驱动**: 通过YAML配置快速创建新的Agent工具
- **标准化**: 统一的API设计和错误处理机制
- **可观测性**: 完善的日志和监控体系
- **快速上手**: 新开发者可在30分钟内理解架构，半天内开始开发

### 扩展性
- **水平扩展**: 支持多实例部署和负载均衡
- **功能扩展**: 新增Agent工具只需配置文件，无需修改核心代码
- **数据扩展**: 支持多数据源接入和查询优化

这个架构设计不仅满足了当前的业务需求，也为未来的功能扩展和性能优化奠定了坚实的基础。通过合理的模块划分和清晰的接口设计，团队可以高效地并行开发，快速响应业务变化。

---

*本文档为ChatBI-MySQL项目的完整架构说明，涵盖了系统设计、技术实现、开发指南和运维监控等各个方面，帮助新开发者在半天内快速上手并开始开发新功能。如有疑问，请联系架构团队。*
